# 网站信息
site_name: YesImBot 官方文档
site_description: 让语言大模型机器人激活灵魂的 Koishi 插件
site_url: https://docs.yesimbot.chat/

# 文档仓库
repo_name: YesWeAreBot/AthenaDocsNG
repo_url: https://github.com/YesWeAreBot/AthenaDocsNG
edit_uri: edit/main/docs/

# 导航结构
nav:
  - 首页: index.md
  - 入门指南:
    - 概述: getting-started/overview.md
    - 安装指南: getting-started/installation.md
    - 快速上手: getting-started/quick-start.md
  - 核心概念:
    - 智能体架构: concepts/agent-architecture.md
    - 意愿系统: concepts/willingness-system.md
    - 记忆与世界状态: concepts/memory-system.md
    - 模型服务: concepts/model-service.md
    - 工具系统: concepts/tool-system.md
  - 配置与参考:
    - 指令参考: reference/commands.md
    - 完整配置参考: reference/configuration.md
  - 扩展插件:
    - 代码解释器: extensions/code-interpreter.md
    - 好感度系统: extensions/favor.md
    - MCP 协议: extensions/mcp.md
  - 进阶玩法:
    - 定制提示词: advanced/custom-prompts.md
    - 性能与成本优化: advanced/performance-optimization.md
  - 最佳实践:
    - 场景范例: best-practices/scenarios.md
  - 附录:
    - 常见问题 (FAQ): appendices/faq.md
    - 故障排除: appendices/troubleshooting.md
    - 术语表: appendices/glossary.md
    - 开发者指南: appendices/developer-guide.md

# 主题配置
theme:
  name: material
  language: zh
  logo: assets/logo.svg
  favicon: assets/favicon.png
  features:
    - navigation.instant
    - navigation.tabs
    - navigation.sections
    - navigation.top
    - navigation.tracking
    - search.suggest
    - search.highlight
    - search.share
    - content.code.copy
    - content.code.annotate
    - toc.follow
  palette:
    # 浅色模式
    - scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-7
        name: 切换到深色模式
    # 深色模式
    - scheme: slate
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-4
        name: 切换到浅色模式
  font:
    text: Roboto
    code: Roboto Mono
  icon:
    repo: fontawesome/brands/github

# 插件配置
plugins:
    - search
    - mermaid2

# Markdown 扩展
markdown_extensions:
  - admonition
  - pymdownx.details
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:mermaid2.fence_mermaid_custom
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.tabbed:
      alternate_style: true
  - attr_list
  - md_in_html
  - tables
  - footnotes
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg

# 额外配置
extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/YesWeAreBot/YesImBot