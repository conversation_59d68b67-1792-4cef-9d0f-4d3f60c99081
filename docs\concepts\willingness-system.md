# 响应的艺术：意愿系统

意愿系统（Willingness System）是 YesImBot 的灵魂，它决定了机器人何时以及如何响应。这套系统告别了简单的“关键词触发”，采用了一套复杂的、模拟生物直觉的动态评分模型。

## 告别冰冷的机器人

传统聊天机器人有两个痛点：

1.  **过于被动：** 只有在被明确触发时（如@或关键词）才说话，缺乏参与感。
2.  **过于主动：** 对每一条消息都进行响应，导致信息过载和刷屏。

YesImBot 的意愿系统旨在解决这个问题，让机器人学会在合适的时机、用合适的方式自然地加入对话。

## 四层决策模型

每一次收到消息，意愿系统都会通过一个四层模型来计算一个“意愿增益”，并将其更新到当前频道的“意愿总分”上。

#### 1. 基础分数 (Base Scores)
- **作用：** 定义对不同消息类型（文本、图片、表情符号等）的初始兴趣。
- **配置路径：** `agentBehavior.willingness.base.scores`

#### 2. 属性加成 (Attribute Bonuses)
- **作用：** 对消息的特定“属性”给予额外加分，以响应重要的社交信号。
- **关键属性：** `atMention` (被@), `isQuote` (回复Bot), `isDirectMessage` (私聊)。
- **配置路径：** `agentBehavior.willingness.attribute.bonuses`

#### 3. 兴趣模型 (Interest Model)
- **作用：** 基于消息内容计算一个“兴趣乘数”，放大或缩小前面的得分。
- **配置路径：** `agentBehavior.willingness.interest`

#### 4. 生命周期 (Lifecycle & Conversion)
- **作用：** 一个动态的“精力条”系统，模拟人类的注意力和疲劳，并将最终的意愿总分转换为具体的行动概率。
- **配置路径：** `agentBehavior.willingness.lifecycle`

---

## 完整计算流程揭秘

理解意愿系统的关键在于其动态和非线性的计算过程：

1.  **计算基础增益 (Gain):**
    -   系统首先根据消息类型（文本、图片、表情）获得一个**基础分**。
    -   然后，根据消息属性（是否被@、是否是回复等）叠加**属性加成**。
    -   最后，将上述总和乘以**兴趣乘数**（如果命中关键词，则乘数更高）。
    -   这一步的结果，我们称之为**基础增益**。

2.  **动态增益放大 (S-Curve Multiplier):**
    -   系统会检查当前频道的**意愿总分**。如果总分已经在一个中等偏高的“兴奋区”，系统会认为 Bot 对当前话题“正感兴趣”，并对本次的**基础增益**应用一个**大于1的放大系数**。这是一个正反馈机制，模拟了“越聊越起劲”的过程。
    -   反之，如果意愿总分很低或接近饱和，放大系数会回归到1或更低。

3.  **更新意愿总分:**
    -   将经过动态放大的增益值，加到当前频道的意愿总分上。总分不会超过 `maxWillingness` 上限。

4.  **弹性衰减 (Elastic Decay):**
    -   在后台，所有频道的意愿总分都会随时间自然衰减（由 `decayHalfLifeSeconds` 控制）。
    -   这种衰减也不是线性的：当意愿总分很高时，衰减速度会**变慢**，模拟了“对感兴趣的话题印象更深刻”的现象。

5.  **概率转换与最终决策:**
    -   当更新后的意愿总分超过 `probabilityThreshold`（响应阈值）时，系统会将其超出部分乘以 `probabilityAmplifier`（概率放大系数），从而计算出一个具体的**回复概率**（0到1之间）。
    -   最后，程序会进行一次“掷骰子”（`Math.random()`），如果随机数小于计算出的概率，Bot 最终决定回复。

6.  **发言成本 (Reply Cost):**
    -   一旦 Bot 决定回复，它的意愿总分会**立即被扣除**一个 `replyCost` 值。这模拟了“发言消耗精力”的过程，是防止 Bot 连续刷屏、实现对话节奏感的关键机制。

---

## “性格”的诞生：人格预设

为了简化复杂的参数配置，我们内置了多种“性格预D设”。选择一种预设，系统会自动为您应用一套精心调整好的意愿系统参数。

-   **`outgoing` (开朗活泼):** 话多，乐于参与，回复门槛低，精力衰减慢。
-   **`professional` (高冷严谨):** 惜字如金，只在被@或讨论专业领域时才发言。回复门槛高，精力衰减快。
-   **`caring` (温柔体贴):** 不主动挑起话题，但在检测到求助类关键词时会第一时间出现。
-   **`default` (默认):** 一个平衡的配置，适合大多数场景。
-   **`custom` (自定义):** 如果您想从头打造一个独一无二的性格，请选择此项并手动调整所有参数。

**配置路径：** `agentBehavior.willingness.personality`

---

## 深入机制：弹性衰减与S型增益

为了让意愿模型更加“人性化”，`WillingnessManager` 在内部实现了一些高级机制：

-   **弹性衰减 (Elastic Decay):**
    -   当意愿值很高时（例如超过了回复门槛），意味着 Agent 正在“高度关注”当前对话。此时，意愿值的自然衰减速度会**变慢**。
    -   这模拟了人类对感兴趣的话题会保持更长时间注意力的现象。

-   **S型曲线增益 (S-Shaped Gain):**
    -   每次消息带来的意愿“增益”不是固定的。当 Agent 对一个话题的兴趣处于“启动区”（意愿值较低）或“饱和区”（意愿值非常高）时，新消息带来的增益会相对较小。
    -   当兴趣处于“陡增区”（意愿值适中）时，新消息会带来**远超基础值**的增益，模拟了“越聊越起劲”的过程。

这两个机制共同作用，使得意愿值的变化更加平滑和自然，避免了因参数设置不当而导致的响应行为突变。