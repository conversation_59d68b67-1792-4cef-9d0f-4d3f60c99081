# 故障排除

**问题：控制台出现 `401 Unauthorized` 或 `Invalid API Key` 错误。**

-   **原因:** 您的 `modelService.providers` 中配置的 `apiKey` 不正确、已失效或额度用尽。
-   **解决:**

    1.  前往对应的模型提供商（如 OpenAI, Anthropic）的后台，确认您的 API Key 是否有效且有可用额度。
    2.  仔细核对配置文件中的 `apiKey` 是否有复制粘贴错误，或遗漏了 `sk-` 前缀。
    3.  如果您使用了代理，请确认代理服务器是否正确转发了认证头。

---

**问题：机器人回复 `[Error: Request timed out]` 或类似超时信息。**

-   **原因:**
    1.  您的网络到模型提供商的 API 服务器之间的连接速度慢或不稳定。
    2.  您本地运行的模型（如 Ollama）响应缓慢或未启动。
    3.  工具执行时间过长。
-   **解决:**
    1.  在 `agentBehavior` 中适当增加 `timeout` 的值（单位：秒）。
    2.  如果是工具超时，在 `capabilities.tools.advanced` 中增加 `timeoutMs` 的值（单位：毫秒）。
    3.  检查您的网络连接。如果可能，为模型提供商配置 `proxy`。
    4.  如果是本地模型，请检查运行该模型的机器负载，并确认服务已启动。

---

**问题：修改配置后不生效。**

-   **原因:**
    1.  如果您手动修改 `yaml` 或 `.json` 配置文件，需要重启 Koishi 或在插件配置页面点击对应插件的“重载”按钮。
    2.  如果您使用 `conf.set` 指令修改，该修改是运行时的临时修改，不会自动写入文件。重启 Koishi 后会丢失。
-   **解决:**
    1.  推荐在 Koishi 的 WebUI 中修改配置，保存后会自动重载。
    2.  使用 `conf.set` 做临时调整后，如果希望持久化，仍需在 WebUI 保存一次。

**终极排错技巧：开启 Debug 模式**
将以下配置应用，可以获得最详细的日志输出，有助于定位绝大多数问题。

```yaml
system:
  logging:
    level: debug
  debug:
    enable: true
```
然后观察 Koishi 控制台的输出。您将能看到详细的意愿计算、Prompt 构建、模型请求和响应等全过程信息。