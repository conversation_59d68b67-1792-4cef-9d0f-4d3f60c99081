# 赋能与扩展：工具系统

工具系统 (`ToolService`) 为 YesImBot 提供了一个统一的框架，使其能够调用外部函数、API 或执行代码，从而与外部世界进行交互。这极大地扩展了 AI 的能力，使其不再局限于纯文本对话。

## 框架与扩展的分离设计

理解工具系统的关键在于，**核心 `yesimbot` 插件本身只提供工具调用的“框架”，而不内置任何具体的工具**。

所有的“工具”都是通过**扩展插件**来注册和实现的。这种设计保证了核心插件的轻量化，并允许用户根据需求按需安装功能。

-   **`ToolService` (框架):** 由核心插件提供。它负责：
    -   从已安装的扩展插件中发现并注册所有可用的工具。
    -   将工具的定义（名称、描述、参数）提供给 AI 模型。
    -   解析 AI 模型返回的工具调用请求（即 Function Calling）。
    -   调用正确的工具函数并传递参数。
    -   将工具的执行结果返回给 AI 模型，以供其生成最终答复。

-   **工具扩展 (实现):**
    -   例如 `koishi-plugin-yesimbot-extension-code-interpreter` 插件，它向 `ToolService` 注册了一个名为 `execute_javascript` 的工具。
    -   例如 `koishi-plugin-yesimbot-extension-favor` 插件，它注册了 `add_favor` 和 `set_favor` 两个工具。
    -   用户也可以开发自己的工具扩展插件。

## 工作流程

1.  **启动时注册:** `ToolService` 扫描所有已安装的、兼容的扩展插件，并将它们提供的工具注册到一个统一的列表中。
2.  **对话时提供:** `ToolService` 将所有已注册且启用的工具的“定义”在每次对话时发送给 AI 模型。
3.  **AI 决策调用:** AI 模型根据用户的提问，判断是否需要以及如何使用这些工具。如果需要，它会返回一个特定格式的“工具调用”请求。
    -   *用户:* `计算 100 * (5+8) 的值`
    -   *AI (返回给系统):* `好的，我需要调用 \`execute_javascript\` 工具，代码是 \`console.log(100 * (5+8))\``
4.  **框架执行返回:** `ToolService` 接收到请求，执行 `execute_javascript` 工具，并将代码的输出结果（例如 `"1300"`）再发送给 AI。
5.  **AI 生成最终回复:** AI 拿到工具的返回结果后，生成面向用户的最终答复。
    -   *AI (回复用户):* `计算结果是 1300。`

## 配置

工具系统的主要配置位于 `capabilities.tools`。

-   **`extra`:** 这是配置各个**扩展插件**的地方。每个扩展插件的配置都位于其对应的键下，例如 `extra.code-interpreter` 或 `extra.favor`。
-   **`advanced`:**
    -   `maxRetry`: 工具执行失败时的最大重试次数。
    -   `retryDelayMs`: 每次重试之间的延迟（毫秒）。
    -   `timeoutMs`: 单个工具执行的超时时间（毫秒）。

具体的工具配置，请参考各个[扩展插件的文档](../extensions/code-interpreter.md)。