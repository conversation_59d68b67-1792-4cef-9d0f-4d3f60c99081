# 欢迎来到 YesImBot 的世界

YesImBot 是一个为 [Koishi](https://koishi.chat/) 框架设计的、高度可扩展的、旨在赋予聊天机器人“灵魂”的插件。

## 机械壳，人类心

我们致力于打破传统聊天机器人机械、被动的交互模式，创造一个能够自然融入群聊环境、像真人一样思考和交流的 AI 伙伴。

它不仅仅是一个问答工具，更是一个具备性格、记忆和动态响应能力的智能体。通过 YesImBot，您可以将一个通用的大语言模型（LLM）转变为一个独一无二的、专属于您的社群的虚拟成员。

## 主要特性

-   **🤖 动态意愿系统:** 独创的四层意愿模型，让机器人拥有“人性化”的发言逻辑，在适当的时机自然地加入对话。
-   **🧠 高级记忆管理:** 结合长期核心记忆与自动摘要的短期记忆，确保对话的连贯性和深度。
-   **☁️ 强大的模型服务:** 支持多种 LLM 提供商，可设置复杂的故障转移和负载均衡策略。
-   **🛠️ 可扩展的工具集:** 通过插件化的工具系统，可以无限扩展机器人的能力边界，执行搜索、计算、代码执行等复杂任务。
-   **⚙️ 深度可定制:** 从性格、提示词到每一个响应细节，几乎所有方面都可进行精细配置。
-   **🚀 易于上手的配置:** 提供创新的交互式 `setup` 指令，引导用户轻松完成初始配置。

## 快速通道

| 入门指南 | 核心功能 |
| :--- | :--- |
| ➡️ **[安装指南](getting-started/installation.md)** | 🧠 **[意愿系统](concepts/willingness-system.md)** |
| ➡️ **[快速上手](getting-started/quick-start.md)** | 💾 **[记忆与世界状态](concepts/memory-system.md)** |

## 项目信息

-   **当前版本:** 3.0.0-beta.4
-   **项目主页:** [GitHub - YesWeAreBot/YesImBot](https://github.com/YesWeAreBot/YesImBot)
-   **许可证:** MIT

!!! tip "开始您的旅程"
    如果您是第一次使用 YesImBot，我们强烈建议您从 **[安装指南](getting-started/installation.md)** 开始，然后通过 **[快速上手](getting-started/quick-start.md)** 完成您的首次配置。
